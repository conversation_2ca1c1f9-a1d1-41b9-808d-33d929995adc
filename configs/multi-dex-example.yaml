# 多DEX同时解析示例配置

app:
  name: "unified-tx-parser"
  version: "1.0.0"
  port: 8081

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

# 以太坊链配置
chains:
  ethereum:
    enabled: true
    rpc_endpoint: "https://eth.rpc.blxrbdn.com"
    chain_id: "ethereum-mainnet"
    batch_size: 10
    timeout: 120
    retry_count: 3
    start_block: 0

# 多DEX协议配置 - 同一条链可以同时运行多个DEX
protocols:
  # 以太坊上的多个DEX同时运行
  uniswap:
    enabled: true
    chain: "ethereum"
    contract_addresses:
      - "******************************************"  # Uniswap V2
      - "******************************************"  # Uniswap V3

  pancakeswap:
    enabled: true
    chain: "ethereum"
    contract_addresses:
      - "******************************************"  # PancakeSwap V3 on Ethereum

  # 注意：sushiswap 协议暂未实现，已移除

processor:
  batch_size: 10
  max_concurrent: 10
  retry_delay: 5
  max_retries: 3

logging:
  level: "info"
  format: "text"
  output: "stdout"

storage:
  type: "influxdb"
  influxdb:
    url: "http://localhost:8086"
    token: "unified-tx-parser-token-2024"
    org: "unified-tx-parser"
    bucket: "multi-dex"
    batch_size: 1000
    flush_time: 10
    precision: "ms"
