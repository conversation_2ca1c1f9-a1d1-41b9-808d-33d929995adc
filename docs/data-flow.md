# 统一交易解析器数据流文档

## 1. 数据流概述

统一交易解析器的数据流从区块链RPC节点开始，经过多个处理阶段，最终存储到目标数据库。整个流程包括数据获取、标准化转换、DEX事件提取、数据存储四个主要阶段。

## 2. 完整数据流图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              数据流处理管道                                        │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 区块链节点   │    │ 链处理器     │    │ DEX提取器    │    │ 存储引擎     │
│             │    │             │    │             │    │             │
│ Ethereum    │───▶│ Ethereum    │───▶│ Uniswap     │───▶│ InfluxDB    │
│ RPC         │    │ Processor   │    │ Extractor   │    │ Storage     │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                           │                   │                   │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ BSC         │───▶│ BSC         │───▶│ PancakeSwap │───▶│ MySQL       │
│ RPC         │    │ Processor   │    │ Extractor   │    │ Storage     │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                           │                   │                   │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Solana      │───▶│ Solana      │───▶│ Jupiter     │───▶│ Redis       │
│ RPC         │    │ Processor   │    │ Extractor   │    │ Progress    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Sui         │───▶│ Sui         │───▶│ Bluefin     │
│ RPC         │    │ Processor   │    │ Extractor   │
└─────────────┘    └─────────────┘    └─────────────┘

                    ┌─────────────────────────────────┐
                    │        核心引擎调度              │
                    │    (Engine.processChain)       │
                    └─────────────────────────────────┘
```

## 3. 详细数据流阶段

### 3.1 阶段一：区块链数据获取

#### 3.1.1 数据源
- **Ethereum**: `https://eth.rpc.blxrbdn.com`
- **BSC**: `https://bsc.publicnode.com`
- **Solana**: `https://api.mainnet-beta.solana.com`
- **Sui**: `https://fullnode.mainnet.sui.io:443`

#### 3.1.2 获取流程
```
1. 核心引擎启动 → Engine.Start()
2. 为每个启用的链启动处理协程 → Engine.processChain()
3. 定时获取最新区块号 → ChainProcessor.GetLatestBlockNumber()
4. 计算处理范围 → startBlock 到 endBlock
5. 批量获取区块数据 → ChainProcessor.GetBlocksByRange()
```

#### 3.1.3 数据格式转换
原始区块链数据 → 统一区块格式 (UnifiedBlock)

```go
// 原始数据示例 (Ethereum)
type Block struct {
    Number       *big.Int
    Hash         common.Hash
    Transactions []*types.Transaction
    // ... 其他字段
}

// 转换后的统一格式
type UnifiedBlock struct {
    BlockNumber  *big.Int
    BlockHash    string
    ChainType    ChainType
    Transactions []UnifiedTransaction
    Events       []UnifiedEvent
    RawData      interface{}
}
```

### 3.2 阶段二：DEX数据提取

#### 3.2.1 提取器调度
```
1. 核心引擎遍历所有注册的DEX提取器
2. 检查提取器是否支持当前区块 → DexExtractors.SupportsBlock()
3. 调用提取器提取数据 → DexExtractors.ExtractDexData()
4. 合并所有提取器的结果
```

#### 3.2.2 Uniswap数据提取流程

以Uniswap为例，详细说明DEX数据提取过程：

```
┌─────────────────────────────────────────────────────────────────┐
│                    Uniswap数据提取流程                           │
└─────────────────────────────────────────────────────────────────┘

输入: UnifiedBlock[]
  │
  ▼
┌─────────────────────────────────────────────────────────────────┐
│ 1. 遍历区块中的所有交易                                          │
│    for _, tx := range block.Transactions                       │
└─────────────────────────────────────────────────────────────────┘
  │
  ▼
┌─────────────────────────────────────────────────────────────────┐
│ 2. 从交易中提取以太坊日志                                        │
│    ethLogs := extractEthereumLogsFromTransaction(&tx)           │
└─────────────────────────────────────────────────────────────────┘
  │
  ▼
┌─────────────────────────────────────────────────────────────────┐
│ 3. 检查是否为Uniswap相关日志                                     │
│    if isUniswapLog(ethLog) {                                   │
│      logType := getLogType(ethLog)                             │
│    }                                                           │
└─────────────────────────────────────────────────────────────────┘
  │
  ▼
┌─────────────────────────────────────────────────────────────────┐
│ 4. 根据日志类型处理                                             │
│    switch logType {                                            │
│      case "swap":     → createTransactionFromLog()             │
│      case "mint":     → createLiquidityFromLog("add")          │
│      case "burn":     → createLiquidityFromLog("remove")       │
│      case "pool_created": → createPoolFromLog()               │
│    }                                                           │
└─────────────────────────────────────────────────────────────────┘
  │
  ▼
输出: DexData {
  Pools: []model.Pool
  Transactions: []model.Transaction
  Liquidities: []model.Liquidity
  Reserves: []model.Reserve
  Tokens: []model.Token
}
```

#### 3.2.3 事件签名识别

Uniswap提取器通过事件签名识别不同类型的操作：

```go
const (
    // Uniswap V2 事件签名
    swapV2EventSig      = "0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822"
    mintV2EventSig      = "0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f"
    burnV2EventSig      = "0xdccd412f0b1252819cb1fd330b93224ca42612892bb3f4f789976e6d81936496"
    pairCreatedEventSig = "0x0d3648bd0f6ba80134a33ba9275ac585d9d315f0ad8355cddefde31afa28d0e9"
    
    // Uniswap V3 事件签名
    swapV3EventSig      = "0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67"
    mintV3EventSig      = "0x7a53080ba414158be7ec69b987b5fb7d07dee101fe85488f0853ae16239d0bde"
    burnV3EventSig      = "0x0c396cd989a39f4459b5fa1aed6a9a8dcdbc45908acfd67e028cd568da98982c"
    poolCreatedEventSig = "0x783cca1c0412dd0d695e784568c96da2e9c22ff989357a2e8b1d9b2b4e6b7118"
)
```

### 3.3 阶段三：数据标准化

#### 3.3.1 交易数据标准化

不同DEX的交易数据被标准化为统一的Transaction模型：

```go
type Transaction struct {
    Addr        string    // 池子地址
    Router      string    // 路由器地址  
    Factory     string    // 工厂地址
    Pool        string    // 池子地址
    Hash        string    // 交易哈希
    From        string    // 发起方地址
    Side        string    // 交易类型 (swap)
    Amount      *big.Int  // 交易金额
    Price       float64   // 交易价格
    Value       float64   // 交易价值
    Time        uint64    // 时间戳
    BlockNumber int64     // 区块号
    Extra       *TransactionExtra // 扩展信息
}
```

#### 3.3.2 流动性数据标准化

```go
type Liquidity struct {
    Addr    string    // 池子地址
    Router  string    // 路由器地址
    Pool    string    // 池子地址
    Hash    string    // 交易哈希
    From    string    // 操作方地址
    Side    string    // 操作类型 (add/remove)
    Amount  *big.Int  // 流动性数量
    Value   float64   // 价值
    Time    uint64    // 时间戳
    Key     string    // 唯一标识
}
```

#### 3.3.3 池子数据标准化

```go
type Pool struct {
    Addr     string            // 池子地址
    Factory  string            // 工厂地址
    Protocol string            // 协议名称 (uniswap/pancakeswap/etc)
    Tokens   map[int]string    // 代币地址映射 {0: token0, 1: token1}
    Fee      int               // 手续费 (basis points)
    Extra    *PoolExtra        // 扩展信息
}
```

### 3.4 阶段四：数据存储

#### 3.4.1 存储引擎选择

系统支持多种存储引擎，根据配置自动选择：

```go
// 存储引擎工厂
func createStorageEngine(cfg *config.Config) (core.StorageEngine, error) {
    switch cfg.Storage.Type {
    case "influxdb":
        return influxdb.NewSimpleInfluxDBStorage(cfg.Storage.InfluxDB)
    case "mysql":
        return mysql.NewMySQLStore(cfg.Storage.MySQL)
    case "redis":
        return redis.NewRedisStorage(cfg.Redis)
    default:
        return nil, fmt.Errorf("不支持的存储类型: %s", cfg.Storage.Type)
    }
}
```

#### 3.4.2 InfluxDB存储流程

InfluxDB作为主要的时序数据存储：

```
1. 接收DexData → StoreDexData()
2. 转换为InfluxDB Point格式
3. 批量写入时序数据
4. 自动刷新缓冲区
```

```go
// InfluxDB数据点示例
point := influxdb2.NewPoint(
    "transactions",                    // measurement
    map[string]string{                // tags
        "chain_type": "ethereum",
        "protocol":   "uniswap",
        "pool":       poolAddress,
    },
    map[string]interface{}{           // fields
        "amount":     transaction.Amount.String(),
        "price":      transaction.Price,
        "value":      transaction.Value,
    },
    transaction.Timestamp,            // timestamp
)
```

#### 3.4.3 MySQL存储流程

MySQL用于存储结构化的关系数据：

```sql
-- 交易表
INSERT INTO unified_transactions (
    id, chain_id, block_number, tx_hash, 
    from_address, to_address, value, 
    gas_used, status, timestamp
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)

-- 池子表  
INSERT INTO unified_pools (
    id, chain_id, protocol, pool_address,
    token0_address, token1_address, fee_tier
) VALUES (?, ?, ?, ?, ?, ?, ?)
```

## 4. 进度跟踪机制

### 4.1 Redis进度存储

```go
type ProcessProgress struct {
    ChainType            ChainType `json:"chain_type"`
    LastProcessedBlock   *big.Int  `json:"last_processed_block"`
    LastUpdateTime       time.Time `json:"last_update_time"`
    TotalTransactions    int64     `json:"total_transactions"`
    TotalEvents          int64     `json:"total_events"`
}
```

### 4.2 断点续传机制

```
1. 启动时从Redis读取上次处理进度
2. 如果没有进度记录，从配置的起始块开始
3. 每批处理完成后更新进度到Redis
4. 支持多链独立进度跟踪
```

## 5. 数据流性能优化

### 5.1 批处理优化

#### 5.1.1 区块批处理
```go
// 配置批处理大小
batchSize := e.config.BatchSize
if config, exists := e.config.ChainConfigs[chainType]; exists {
    batchSize = config.BatchSize
}

// 批量获取区块
endBlock := big.NewInt(0).Add(startBlock, big.NewInt(int64(batchSize)))
blocks, err := processor.GetBlocksByRange(ctx, startBlock, endBlock)
```

#### 5.1.2 数据库批写入
```go
// InfluxDB批写入
for _, transaction := range transactions {
    point := influxdb2.NewPoint(...)
    writeAPI.WritePoint(point)
}
writeAPI.Flush() // 批量刷新
```

### 5.2 并发处理

#### 5.2.1 多链并发
```go
// 为每个链启动独立的处理协程
for chainType, processor := range e.chainProcessors {
    go e.processChain(chainType, processor)
}
```

#### 5.2.2 超时控制
```go
// 设置处理超时
txCtx, txCancel := context.WithTimeout(e.ctx, time.Minute*5)
defer txCancel()

blocks, err := processor.GetBlocksByRange(txCtx, startBlock, endBlock)
```

### 5.3 错误处理和重试

#### 5.3.1 指数退避重试
```go
func (e *EthereumProcessor) getBlockWithRetry(ctx context.Context, blockNum *big.Int, maxRetries int) (*types.Block, error) {
    var lastErr error
    for i := 0; i < maxRetries; i++ {
        block, err := e.client.BlockByNumber(ctx, blockNum)
        if err == nil {
            return block, nil
        }

        lastErr = err
        backoff := time.Duration(i+1) * time.Second
        time.Sleep(backoff)
    }
    return nil, lastErr
}
```

#### 5.3.2 错误跳过机制
```go
// 跳过有问题的区块，继续处理
if err != nil {
    log.Printf("⚠️ 跳过无法获取的区块 %s: %v", blockNum.String(), err)
    continue
}
```

## 6. 数据质量保证

### 6.1 数据验证

#### 6.1.1 交易数据验证
```go
func validateTransaction(tx *model.Transaction) error {
    if tx.Hash == "" {
        return errors.New("交易哈希不能为空")
    }
    if tx.Amount == nil || tx.Amount.Cmp(big.NewInt(0)) < 0 {
        return errors.New("交易金额无效")
    }
    return nil
}
```

#### 6.1.2 地址格式验证
```go
func validateAddress(addr string) bool {
    return common.IsHexAddress(addr)
}
```

### 6.2 重复数据处理

#### 6.2.1 唯一键设计
```go
// 交易唯一键: chain_type + tx_hash + event_index
key := fmt.Sprintf("%s_%s_%d", chainType, txHash, eventIndex)

// 池子唯一键: chain_type + protocol + pool_address
poolKey := fmt.Sprintf("%s_%s_%s", chainType, protocol, poolAddress)
```

#### 6.2.2 幂等性保证
```sql
-- MySQL使用ON DUPLICATE KEY UPDATE
INSERT INTO unified_transactions (...) VALUES (...)
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- InfluxDB使用时间戳作为唯一标识
```

## 7. 监控和日志

### 7.1 处理日志

系统提供详细的处理日志，便于监控和调试：

```
📊 [ETHEREUM] 区块 19000000-19000010
📥 [ETHEREUM] 10个区块, 150笔交易 (2.3s)
📊 [ETHEREUM] DEX数据提取完成: 25条记录 (0.5s)
   - 池子: 2, 交易: 18, 流动性: 5, 储备: 0, 代币: 0
💾 [ETHEREUM] 存储完成: 25条DEX记录 (0.2s)
✅ [ETHEREUM] 处理完成: 10区块, 150交易, 25条DEX记录 (区块:19000010)
```

### 7.2 性能指标

#### 7.2.1 处理速度指标
- 区块处理速度 (blocks/second)
- 交易处理速度 (transactions/second)
- DEX事件提取速度 (events/second)
- 数据存储速度 (records/second)

#### 7.2.2 系统健康指标
- RPC连接状态
- 数据库连接状态
- 内存使用情况
- 处理延迟统计

### 7.3 API监控接口

```http
GET /api/v1/stats          # 系统整体统计
GET /api/v1/health         # 健康检查
GET /api/v1/progress       # 处理进度
GET /api/v1/storage/stats  # 存储统计
```

## 8. 数据流总结

统一交易解析器的数据流具有以下特点：

1. **多链统一**: 支持多条区块链的数据统一处理
2. **实时处理**: 支持实时区块数据获取和处理
3. **模块化设计**: 链处理器、DEX提取器、存储引擎独立可扩展
4. **高性能**: 批处理、并发、缓存等多种优化手段
5. **高可靠**: 错误重试、进度跟踪、数据验证等保证机制
6. **可观测**: 详细日志、性能指标、监控接口

整个数据流从区块链原始数据到最终的结构化存储，实现了完整的DEX交易数据处理管道。
