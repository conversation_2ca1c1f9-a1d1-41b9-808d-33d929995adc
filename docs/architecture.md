# 统一交易解析器架构文档

## 1. 项目概述

统一交易解析器（Unified Transaction Parser）是一个多链DEX交易数据提取和分析系统，支持从多个区块链（Ethereum、BSC、Solana、Sui）提取去中心化交易所（DEX）的交易数据，并将其标准化存储。

### 1.1 核心特性

- **多链支持**：支持 Ethereum、BSC、Solana、Sui 四条主流区块链
- **多DEX协议**：支持 Uniswap、PancakeSwap、Jupiter、Bluefin 等主流DEX协议
- **统一数据模型**：将不同链和协议的数据标准化为统一格式
- **实时处理**：支持实时区块数据获取和处理
- **多存储引擎**：支持 MySQL、InfluxDB、Redis 等多种存储方案
- **进度跟踪**：Redis-based 进度跟踪，支持断点续传
- **RESTful API**：提供完整的查询和监控API

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        统一交易解析器                              │
├─────────────────────────────────────────────────────────────────┤
│                      API 服务层 (Gin)                           │
├─────────────────────────────────────────────────────────────────┤
│                    核心引擎 (Engine)                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   链处理器层     │   DEX提取器层    │      存储引擎层              │
│                │                │                             │
│ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────┬─────────────┐ │
│ │ Ethereum    │ │ │ Uniswap     │ │ │ InfluxDB    │ MySQL       │ │
│ │ Processor   │ │ │ Extractor   │ │ │ Storage     │ Storage     │ │
│ └─────────────┘ │ └─────────────┘ │ └─────────────┴─────────────┘ │
│ ┌─────────────┐ │ ┌─────────────┐ │                             │
│ │ BSC         │ │ │ PancakeSwap │ │ ┌─────────────────────────────┐ │
│ │ Processor   │ │ │ Extractor   │ │ │ Redis Progress Tracker      │ │
│ └─────────────┘ │ └─────────────┘ │ └─────────────────────────────┘ │
│ ┌─────────────┐ │ ┌─────────────┐ │                             │
│ │ Solana      │ │ │ Jupiter     │ │                             │
│ │ Processor   │ │ │ Extractor   │ │                             │
│ └─────────────┘ │ └─────────────┘ │                             │
│ ┌─────────────┐ │ ┌─────────────┐ │                             │
│ │ Sui         │ │ │ Bluefin     │ │                             │
│ │ Processor   │ │ │ Extractor   │ │                             │
│ └─────────────┘ │ └─────────────┘ │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 核心引擎 (Engine)
- **位置**: `pkg/core/engine.go`
- **职责**: 
  - 统一调度和管理所有链处理器
  - 协调DEX数据提取器
  - 管理数据存储流程
  - 进度跟踪和错误处理

#### 2.2.2 链处理器 (ChainProcessor)
- **位置**: `pkg/chains/`
- **实现**: 
  - `ethereum/processor.go` - 以太坊链处理器
  - `bsc/processor.go` - BSC链处理器  
  - `solana/processor.go` - Solana链处理器
  - `sui/processor.go` - Sui链处理器
- **职责**:
  - 连接区块链RPC节点
  - 获取区块和交易数据
  - 将原始数据转换为统一格式

#### 2.2.3 DEX提取器 (DexExtractors)
- **位置**: `pkg/dexs/`
- **实现**:
  - `uniswap.go` - Uniswap协议提取器
  - `pancakeswap.go` - PancakeSwap协议提取器
  - `jupiter.go` - Jupiter协议提取器
  - `bluefin.go` - Bluefin协议提取器
- **职责**:
  - 识别DEX相关交易
  - 解析智能合约事件
  - 提取交易、流动性、池子等数据

#### 2.2.4 存储引擎 (StorageEngine)
- **位置**: `pkg/storage/`
- **实现**:
  - `influxdb/` - 时序数据库存储
  - `mysql/` - 关系型数据库存储
  - `redis/` - 缓存和进度跟踪
- **职责**:
  - 数据持久化
  - 查询接口
  - 数据统计

## 3. 数据模型

### 3.1 核心数据结构

#### 3.1.1 统一区块 (UnifiedBlock)
```go
type UnifiedBlock struct {
    BlockNumber  *big.Int              // 区块号
    BlockHash    string                // 区块哈希
    ChainType    ChainType             // 链类型
    ChainID      string                // 链ID
    ParentHash   string                // 父区块哈希
    Timestamp    time.Time             // 时间戳
    TxCount      int                   // 交易数量
    Transactions []UnifiedTransaction  // 交易列表
    Events       []UnifiedEvent        // 事件列表
    RawData      interface{}           // 原始数据
}
```

#### 3.1.2 统一交易 (UnifiedTransaction)
```go
type UnifiedTransaction struct {
    TxHash       string            // 交易哈希
    ChainType    ChainType         // 链类型
    ChainID      string            // 链ID
    BlockNumber  *big.Int          // 区块号
    FromAddress  string            // 发送方地址
    ToAddress    string            // 接收方地址
    Value        *big.Int          // 转账金额
    GasUsed      *big.Int          // 消耗Gas
    Status       TransactionStatus // 交易状态
    Timestamp    time.Time         // 时间戳
    RawData      interface{}       // 原始数据
}
```

#### 3.1.3 DEX数据 (DexData)
```go
type DexData struct {
    Pools        []model.Pool        // 池子数据
    Transactions []model.Transaction // 交易数据
    Liquidities  []model.Liquidity   // 流动性数据
    Reserves     []model.Reserve     // 储备数据
    Tokens       []model.Token       // 代币数据
}
```

### 3.2 业务数据模型

#### 3.2.1 交易模型 (Transaction)
```go
type Transaction struct {
    Addr        string    // 池子地址
    Router      string    // 路由器地址
    Factory     string    // 工厂地址
    Pool        string    // 池子地址
    Hash        string    // 交易哈希
    From        string    // 发起方
    Side        string    // 交易类型 (swap/add/remove)
    Amount      *big.Int  // 交易金额
    Price       float64   // 价格
    Value       float64   // 价值
    Time        uint64    // 时间戳
    BlockNumber int64     // 区块号
}
```

#### 3.2.2 池子模型 (Pool)
```go
type Pool struct {
    Addr     string            // 池子地址
    Factory  string            // 工厂地址
    Protocol string            // 协议名称
    Tokens   map[int]string    // 代币映射
    Fee      int               // 手续费
}
```

#### 3.2.3 流动性模型 (Liquidity)
```go
type Liquidity struct {
    Addr    string    // 池子地址
    Router  string    // 路由器地址
    Pool    string    // 池子地址
    Hash    string    // 交易哈希
    From    string    // 操作方
    Side    string    // 操作类型 (add/remove)
    Amount  *big.Int  // 流动性数量
    Value   float64   // 价值
    Time    uint64    // 时间戳
}
```

## 4. 接口设计

### 4.1 核心接口

#### 4.1.1 链处理器接口 (ChainProcessor)
```go
type ChainProcessor interface {
    GetChainType() ChainType
    GetChainID() string
    GetLatestBlockNumber(ctx context.Context) (*big.Int, error)
    GetBlocksByRange(ctx context.Context, startBlock, endBlock *big.Int) ([]UnifiedBlock, error)
    GetBlock(ctx context.Context, blockNumber *big.Int) (*UnifiedBlock, error)
    GetTransaction(ctx context.Context, txHash string) (*UnifiedTransaction, error)
    HealthCheck(ctx context.Context) error
}
```

#### 4.1.2 DEX提取器接口 (DexExtractors)
```go
type DexExtractors interface {
    GetSupportedProtocols() []string
    GetSupportedChains() []ChainType
    ExtractDexData(ctx context.Context, blocks []UnifiedBlock) (*DexData, error)
    SupportsBlock(block *UnifiedBlock) bool
}
```

#### 4.1.3 存储引擎接口 (StorageEngine)
```go
type StorageEngine interface {
    StoreBlocks(ctx context.Context, blocks []UnifiedBlock) error
    StoreTransactions(ctx context.Context, txs []UnifiedTransaction) error
    StoreBusinessEvents(ctx context.Context, events []BusinessEvent) error
    StoreDexData(ctx context.Context, dexData *DexData) error
    GetStorageStats(ctx context.Context) (map[string]interface{}, error)
    HealthCheck(ctx context.Context) error
}
```

## 5. 配置系统

### 5.1 配置文件结构

系统支持两种配置模式：
1. **统一配置模式**: `configs/config.yaml` - 所有链和协议的配置
2. **链特定配置模式**: `configs/{chain}.yaml` - 单链专用配置

### 5.2 主要配置项

```yaml
# 应用配置
app:
  name: "unified-tx-parser"
  version: "1.0.0"
  port: 8081

# 链配置
chains:
  ethereum:
    enabled: true
    rpc_endpoint: "https://eth.rpc.blxrbdn.com"
    chain_id: "ethereum-mainnet"
    batch_size: 10
    timeout: 120

# 协议配置
protocols:
  uniswap:
    enabled: true
    chain: "ethereum"
    contract_addresses: []

# 存储配置
storage:
  type: "influxdb"
  influxdb:
    url: "http://localhost:8086"
    token: "unified-tx-parser-token-2024"
    org: "unified-tx-parser"
    bucket: "blockchain-data"
```

## 6. 部署架构

### 6.1 Docker 容器化部署

系统提供完整的Docker容器化部署方案：

```yaml
# docker-compose.yml
version: '3.8'
services:
  parser:
    build: .
    ports:
      - "8081:8081"
    depends_on:
      - redis
      - influxdb
      - mysql
    environment:
      - CHAIN_TYPE=sui

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  influxdb:
    image: influxdb:2.7
    ports:
      - "8086:8086"
    environment:
      - INFLUXDB_DB=blockchain-data

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=unified_tx_parser
```

### 6.2 监控和可观测性

- **Grafana 仪表板**: 实时监控系统性能和数据处理状态
- **InfluxDB 指标**: 存储时序性能数据
- **RESTful API**: 提供系统状态查询接口

## 7. 扩展性设计

### 7.1 新链接入

添加新的区块链支持需要：

1. 实现 `ChainProcessor` 接口
2. 在 `pkg/chains/` 下创建新的处理器
3. 在配置文件中添加链配置
4. 在主程序中注册新的处理器

### 7.2 新DEX协议接入

添加新的DEX协议支持需要：

1. 实现 `DexExtractors` 接口
2. 在 `pkg/dexs/` 下创建新的提取器
3. 在工厂类中注册新的提取器
4. 在配置文件中添加协议配置

### 7.3 新存储引擎接入

添加新的存储引擎需要：

1. 实现 `StorageEngine` 接口
2. 在 `pkg/storage/` 下创建新的存储实现
3. 在主程序中添加存储引擎创建逻辑

## 8. 性能优化

### 8.1 批处理优化

- **区块批处理**: 支持批量获取区块数据，减少RPC调用
- **数据库批写入**: 支持批量写入数据库，提高写入性能
- **并发处理**: 支持多链并发处理，提高整体吞吐量

### 8.2 缓存策略

- **Redis缓存**: 缓存频繁查询的数据
- **进度缓存**: 实时缓存处理进度，支持断点续传
- **连接池**: 数据库连接池优化，减少连接开销

### 8.3 错误处理和重试

- **指数退避重试**: 网络错误自动重试
- **错误跟踪**: 详细的错误日志和统计
- **健康检查**: 定期检查各组件健康状态
