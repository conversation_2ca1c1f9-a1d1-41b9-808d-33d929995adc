# 统一交易解析器文档

## 📋 文档概览

本文档集合提供了统一交易解析器（Unified Transaction Parser）的完整技术文档，包括系统架构、数据流设计、API接口等详细信息。

## 📚 文档结构

### 1. [架构文档](./architecture.md)
- **系统整体架构设计**
- **核心组件详细说明**
- **接口设计规范**
- **数据模型定义**
- **配置系统说明**
- **部署架构方案**
- **扩展性设计**
- **性能优化策略**

### 2. [数据流文档](./data-flow.md)
- **完整数据流管道**
- **四个处理阶段详解**
- **DEX数据提取流程**
- **数据标准化过程**
- **存储引擎选择**
- **进度跟踪机制**
- **性能优化方案**
- **监控和日志系统**

## 🏗️ 系统架构概览

统一交易解析器是一个多链DEX交易数据提取和分析系统，采用模块化设计：

```
┌─────────────────────────────────────────────────────────────────┐
│                        统一交易解析器                              │
├─────────────────────────────────────────────────────────────────┤
│                      API 服务层 (Gin)                           │
├─────────────────────────────────────────────────────────────────┤
│                    核心引擎 (Engine)                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   链处理器层     │   DEX提取器层    │      存储引擎层              │
│                │                │                             │
│ • Ethereum     │ • Uniswap      │ • InfluxDB                  │
│ • BSC          │ • PancakeSwap  │ • MySQL                     │
│ • Solana       │ • Jupiter      │ • Redis                     │
│ • Sui          │ • Bluefin      │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

## 🔄 数据流概览

数据处理分为四个主要阶段：

1. **数据获取阶段**: 从区块链RPC节点获取原始区块数据
2. **DEX提取阶段**: 识别和提取DEX相关的交易事件
3. **数据标准化阶段**: 将不同协议的数据转换为统一格式
4. **数据存储阶段**: 将处理后的数据存储到目标数据库

## 🚀 核心特性

### 多链支持
- **Ethereum**: 支持Uniswap等主流DEX协议
- **BSC**: 支持PancakeSwap等BSC生态DEX
- **Solana**: 支持Jupiter等Solana生态DEX
- **Sui**: 支持Bluefin等Sui生态DEX

### 统一数据模型
- **交易数据**: 标准化的DEX交易记录
- **流动性数据**: 统一的流动性操作记录
- **池子数据**: 标准化的交易池信息
- **代币数据**: 统一的代币元数据

### 多存储引擎
- **InfluxDB**: 时序数据存储，适合大量交易数据
- **MySQL**: 关系型数据存储，适合结构化查询
- **Redis**: 缓存和进度跟踪

### 实时处理
- **实时区块监听**: 支持实时获取最新区块数据
- **批处理优化**: 支持批量处理提高性能
- **断点续传**: 支持进度跟踪和故障恢复

## 📊 支持的DEX协议

| 协议 | 链 | 版本 | 支持功能 |
|------|----|----- |----------|
| Uniswap | Ethereum | V2/V3 | Swap, Add/Remove Liquidity, Pool Creation |
| PancakeSwap | BSC | V2/V3 | Swap, Add/Remove Liquidity, Pool Creation |
| Jupiter | Solana | V1 | Swap, Liquidity Operations |
| Bluefin | Sui | V1 | Swap, Liquidity Operations |

## 🛠️ 技术栈

### 后端技术
- **Go 1.21+**: 主要开发语言
- **Gin**: HTTP Web框架
- **Ethereum Go客户端**: 以太坊链交互
- **Solana Go SDK**: Solana链交互
- **Sui Go SDK**: Sui链交互

### 存储技术
- **InfluxDB 2.x**: 时序数据库
- **MySQL 8.0**: 关系型数据库
- **Redis 7.x**: 缓存和会话存储

### 部署技术
- **Docker**: 容器化部署
- **Docker Compose**: 多服务编排
- **Grafana**: 监控仪表板

## 📈 性能指标

### 处理能力
- **区块处理速度**: 10-50 blocks/second (取决于链类型)
- **交易处理速度**: 1000-5000 transactions/second
- **DEX事件提取**: 500-2000 events/second
- **数据存储速度**: 2000-10000 records/second

### 资源消耗
- **内存使用**: 512MB - 2GB (取决于批处理大小)
- **CPU使用**: 2-8 cores (取决于并发链数量)
- **存储空间**: 根据数据量线性增长

## 🔧 快速开始

### 1. 环境准备
```bash
# 安装依赖
go mod download

# 启动基础服务
docker-compose up -d redis influxdb mysql
```

### 2. 配置文件
```bash
# 复制配置模板
cp configs/config.yaml.example configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

### 3. 启动服务
```bash
# 启动解析器
go run cmd/parser/main.go

# 或指定特定链
go run cmd/parser/main.go -chain ethereum
```

### 4. 验证运行
```bash
# 检查系统状态
curl http://localhost:8081/api/v1/health

# 查看处理进度
curl http://localhost:8081/api/v1/progress
```

## 📖 API文档

### 系统状态接口
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/stats` - 系统统计
- `GET /api/v1/progress` - 处理进度

### 数据查询接口
- `GET /api/v1/transactions` - 查询交易数据
- `GET /api/v1/pools` - 查询池子数据
- `GET /api/v1/liquidities` - 查询流动性数据

### 存储统计接口
- `GET /api/v1/storage/stats` - 存储统计信息

## 🔍 监控和调试

### 日志系统
系统提供详细的结构化日志：
```
📊 [ETHEREUM] 区块 19000000-19000010
📥 [ETHEREUM] 10个区块, 150笔交易 (2.3s)
📊 [ETHEREUM] DEX数据提取完成: 25条记录 (0.5s)
💾 [ETHEREUM] 存储完成: 25条DEX记录 (0.2s)
✅ [ETHEREUM] 处理完成: 10区块, 150交易, 25条DEX记录
```

### Grafana仪表板
- 实时处理速度监控
- 系统资源使用情况
- 错误率和成功率统计
- 各链处理进度对比

## 🤝 贡献指南

### 添加新链支持
1. 实现 `ChainProcessor` 接口
2. 在 `pkg/chains/` 下创建新的处理器
3. 添加配置文件支持
4. 更新文档

### 添加新DEX协议
1. 实现 `DexExtractors` 接口
2. 在 `pkg/dexs/` 下创建新的提取器
3. 在工厂类中注册
4. 添加测试用例

### 代码规范
- 遵循Go官方代码规范
- 添加充分的单元测试
- 提供详细的注释文档
- 使用结构化日志

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者
- 参与项目讨论

---

*最后更新: 2024年7月*
