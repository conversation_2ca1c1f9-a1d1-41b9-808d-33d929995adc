package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/utils"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLStore MySQL存储引擎
type MySQLStore struct {
	db     *sql.DB
	config *MySQLConfig
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host         string `json:"host"`
	Port         int    `json:"port"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	Database     string `json:"database"`
	MaxOpenConns int    `json:"max_open_conns"`
	MaxIdleConns int    `json:"max_idle_conns"`
}

// NewMySQLStore 创建MySQL存储引擎
func NewMySQLStore(config *MySQLConfig) (*MySQLStore, error) {
	if config == nil {
		config = &MySQLConfig{
			Host:         "localhost",
			Port:         3306,
			Username:     "root",
			Password:     "password",
			Database:     "unified_tx_parser",
			MaxOpenConns: 100,
			MaxIdleConns: 10,
		}
	}

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Username, config.Password, config.Host, config.Port, config.Database)

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(time.Hour)

	store := &MySQLStore{
		db:     db,
		config: config,
	}

	// 测试连接
	if err := store.HealthCheck(context.Background()); err != nil {
		return nil, fmt.Errorf("MySQL健康检查失败: %w", err)
	}

	// 初始化表结构
	if err := store.initTables(); err != nil {
		return nil, fmt.Errorf("初始化表结构失败: %w", err)
	}

	log.Printf("✅ MySQL存储引擎初始化成功: %s:%d/%s", config.Host, config.Port, config.Database)
	return store, nil
}

// initTables 初始化表结构
func (m *MySQLStore) initTables() error {
	tables := []string{
		// 统一交易表
		`CREATE TABLE IF NOT EXISTS unified_transactions (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			tx_hash VARCHAR(128) NOT NULL,
			chain_type VARCHAR(32) NOT NULL,
			chain_id VARCHAR(64) NOT NULL,
			block_number BIGINT,
			block_hash VARCHAR(128),
			tx_index INT,
			from_address VARCHAR(128),
			to_address VARCHAR(128),
			value DECIMAL(65,0),
			gas_limit BIGINT,
			gas_used BIGINT,
			gas_price BIGINT,
			status VARCHAR(32),
			timestamp TIMESTAMP,
			raw_data JSON,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_tx_hash_chain (tx_hash, chain_type),
			INDEX idx_chain_block (chain_type, block_number),
			INDEX idx_timestamp (timestamp),
			INDEX idx_from_address (from_address),
			INDEX idx_to_address (to_address)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,

		// 统一业务事件表
		`CREATE TABLE IF NOT EXISTS unified_business_events (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			event_id VARCHAR(256) NOT NULL UNIQUE,
			event_type VARCHAR(64) NOT NULL,
			protocol VARCHAR(64) NOT NULL,
			tx_hash VARCHAR(128) NOT NULL,
			chain_type VARCHAR(32) NOT NULL,
			event_data JSON,
			timestamp TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_tx_hash (tx_hash),
			INDEX idx_event_type (event_type),
			INDEX idx_protocol (protocol),
			INDEX idx_chain_type (chain_type),
			INDEX idx_timestamp (timestamp)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,

		// 处理进度表
		`CREATE TABLE IF NOT EXISTS processing_progress (
			chain_type VARCHAR(32) PRIMARY KEY,
			last_processed_block BIGINT NOT NULL DEFAULT 0,
			last_update_time TIMESTAMP,
			total_transactions BIGINT DEFAULT 0,
			total_events BIGINT DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,
	}

	for _, createSQL := range tables {
		if _, err := m.db.Exec(createSQL); err != nil {
			return fmt.Errorf("创建表失败: %w", err)
		}
	}

	log.Printf("💾 MySQL表结构初始化完成")
	return nil
}

// StoreTransactions 存储交易
func (m *MySQLStore) StoreTransactions(ctx context.Context, txs []core.UnifiedTransaction) error {
	if len(txs) == 0 {
		return nil
	}

	// 开始事务
	tx, err := m.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 准备批量插入SQL
	stmt, err := tx.PrepareContext(ctx, `
		INSERT INTO unified_transactions (
			tx_hash, chain_type, chain_id, block_number, block_hash, tx_index,
			from_address, to_address, value, gas_limit, gas_used, gas_price,
			status, timestamp, raw_data
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			block_number = VALUES(block_number),
			block_hash = VALUES(block_hash),
			tx_index = VALUES(tx_index),
			from_address = VALUES(from_address),
			to_address = VALUES(to_address),
			value = VALUES(value),
			gas_limit = VALUES(gas_limit),
			gas_used = VALUES(gas_used),
			gas_price = VALUES(gas_price),
			status = VALUES(status),
			timestamp = VALUES(timestamp),
			raw_data = VALUES(raw_data),
			updated_at = CURRENT_TIMESTAMP
	`)
	if err != nil {
		return fmt.Errorf("准备SQL语句失败: %w", err)
	}
	defer stmt.Close()

	// 批量插入
	for _, transaction := range txs {
		rawDataJSON, _ := utils.ToJSON(transaction.RawData)

		_, err := stmt.ExecContext(ctx,
			transaction.TxHash,
			string(transaction.ChainType),
			transaction.ChainID,
			utils.BigIntToNullInt64(transaction.BlockNumber),
			utils.StringToNullString(transaction.BlockHash),
			utils.IntToNullInt(transaction.TxIndex),
			utils.StringToNullString(transaction.FromAddress),
			utils.StringToNullString(transaction.ToAddress),
			utils.BigIntToNullString(transaction.Value),
			utils.BigIntToNullInt64(transaction.GasLimit),
			utils.BigIntToNullInt64(transaction.GasUsed),
			utils.BigIntToNullInt64(transaction.GasPrice),
			string(transaction.Status),
			transaction.Timestamp,
			rawDataJSON,
		)
		if err != nil {
			return fmt.Errorf("插入交易失败 (hash: %s): %w", transaction.TxHash, err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Printf("💾 存储了 %d 笔交易", len(txs))
	return nil
}

// StoreBlocks 存储区块数据 - MySQL存储引擎不支持区块存储
func (m *MySQLStore) StoreBlocks(ctx context.Context, blocks []core.UnifiedBlock) error {
	// MySQL存储引擎专注于交易和事件存储，不支持区块存储
	return fmt.Errorf("MySQL存储引擎不支持区块存储，请使用InfluxDB")
}

// StoreDexData 存储DEX数据 - MySQL存储引擎不支持DEX数据存储
func (m *MySQLStore) StoreDexData(ctx context.Context, dexData *core.DexData) error {
	// MySQL存储引擎专注于基础交易和事件存储，不支持复杂的DEX数据结构
	return fmt.Errorf("MySQL存储引擎不支持DEX数据存储，请使用InfluxDB")
}

// StoreBusinessEvents 存储业务事件
func (m *MySQLStore) StoreBusinessEvents(ctx context.Context, events []core.BusinessEvent) error {
	if len(events) == 0 {
		return nil
	}

	// 开始事务
	tx, err := m.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 准备批量插入SQL
	stmt, err := tx.PrepareContext(ctx, `
		INSERT INTO unified_business_events (
			event_id, event_type, protocol, tx_hash, chain_type, event_data, timestamp
		) VALUES (?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			event_type = VALUES(event_type),
			protocol = VALUES(protocol),
			tx_hash = VALUES(tx_hash),
			chain_type = VALUES(chain_type),
			event_data = VALUES(event_data),
			timestamp = VALUES(timestamp),
			updated_at = CURRENT_TIMESTAMP
	`)
	if err != nil {
		return fmt.Errorf("准备SQL语句失败: %w", err)
	}
	defer stmt.Close()

	// 批量插入
	for _, event := range events {
		eventDataJSON, _ := utils.ToJSON(event.Data)

		_, err := stmt.ExecContext(ctx,
			event.EventID,
			string(event.EventType),
			event.Protocol,
			event.TxHash,
			string(event.ChainType),
			eventDataJSON,
			event.Timestamp,
		)
		if err != nil {
			return fmt.Errorf("插入事件失败 (id: %s): %w", event.EventID, err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Printf("💾 存储了 %d 个业务事件", len(events))
	return nil
}

// GetTransactionsByHash 根据哈希获取交易
func (m *MySQLStore) GetTransactionsByHash(ctx context.Context, hashes []string) ([]core.UnifiedTransaction, error) {
	if len(hashes) == 0 {
		return nil, nil
	}

	// 构建IN查询
	placeholders := strings.Repeat("?,", len(hashes)-1) + "?"
	query := fmt.Sprintf(`
		SELECT tx_hash, chain_type, chain_id, block_number, block_hash, tx_index,
			   from_address, to_address, value, gas_limit, gas_used, gas_price,
			   status, timestamp, raw_data
		FROM unified_transactions 
		WHERE tx_hash IN (%s)
	`, placeholders)

	// 转换参数
	args := make([]interface{}, len(hashes))
	for i, hash := range hashes {
		args[i] = hash
	}

	rows, err := m.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询交易失败: %w", err)
	}
	defer rows.Close()

	var transactions []core.UnifiedTransaction
	for rows.Next() {
		tx, err := m.scanTransaction(rows)
		if err != nil {
			return nil, fmt.Errorf("扫描交易失败: %w", err)
		}
		transactions = append(transactions, tx)
	}

	return transactions, nil
}

// GetEventsByTxHash 根据交易哈希获取事件
func (m *MySQLStore) GetEventsByTxHash(ctx context.Context, txHash string) ([]core.BusinessEvent, error) {
	query := `
		SELECT event_id, event_type, protocol, tx_hash, chain_type, event_data, timestamp
		FROM unified_business_events 
		WHERE tx_hash = ?
		ORDER BY id
	`

	rows, err := m.db.QueryContext(ctx, query, txHash)
	if err != nil {
		return nil, fmt.Errorf("查询事件失败: %w", err)
	}
	defer rows.Close()

	var events []core.BusinessEvent
	for rows.Next() {
		event, err := m.scanBusinessEvent(rows)
		if err != nil {
			return nil, fmt.Errorf("扫描事件失败: %w", err)
		}
		events = append(events, event)
	}

	return events, nil
}

// GetEventsByType 根据类型获取事件
func (m *MySQLStore) GetEventsByType(ctx context.Context, eventType core.BusinessEventType, limit int) ([]core.BusinessEvent, error) {
	query := `
		SELECT event_id, event_type, protocol, tx_hash, chain_type, event_data, timestamp
		FROM unified_business_events 
		WHERE event_type = ?
		ORDER BY timestamp DESC
		LIMIT ?
	`

	rows, err := m.db.QueryContext(ctx, query, string(eventType), limit)
	if err != nil {
		return nil, fmt.Errorf("查询事件失败: %w", err)
	}
	defer rows.Close()

	var events []core.BusinessEvent
	for rows.Next() {
		event, err := m.scanBusinessEvent(rows)
		if err != nil {
			return nil, fmt.Errorf("扫描事件失败: %w", err)
		}
		events = append(events, event)
	}

	return events, nil
}

// GetStorageStats 获取存储统计
func (m *MySQLStore) GetStorageStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 交易统计
	var txCount int64
	err := m.db.QueryRowContext(ctx, "SELECT COUNT(*) FROM unified_transactions").Scan(&txCount)
	if err != nil {
		return nil, fmt.Errorf("查询交易统计失败: %w", err)
	}
	stats["total_transactions"] = txCount

	// 事件统计
	var eventCount int64
	err = m.db.QueryRowContext(ctx, "SELECT COUNT(*) FROM unified_business_events").Scan(&eventCount)
	if err != nil {
		return nil, fmt.Errorf("查询事件统计失败: %w", err)
	}
	stats["total_events"] = eventCount

	// 按链统计
	chainStats := make(map[string]map[string]int64)
	rows, err := m.db.QueryContext(ctx, `
		SELECT chain_type, 
			   COUNT(*) as tx_count,
			   (SELECT COUNT(*) FROM unified_business_events e WHERE e.chain_type = t.chain_type) as event_count
		FROM unified_transactions t 
		GROUP BY chain_type
	`)
	if err != nil {
		return nil, fmt.Errorf("查询链统计失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var chainType string
		var txCount, eventCount int64
		if err := rows.Scan(&chainType, &txCount, &eventCount); err != nil {
			return nil, fmt.Errorf("扫描链统计失败: %w", err)
		}
		chainStats[chainType] = map[string]int64{
			"transactions": txCount,
			"events":       eventCount,
		}
	}
	stats["chain_stats"] = chainStats

	return stats, nil
}

// HealthCheck 健康检查
func (m *MySQLStore) HealthCheck(ctx context.Context) error {
	return m.db.PingContext(ctx)
}

// Close 关闭连接
func (m *MySQLStore) Close() error {
	return m.db.Close()
}

// scanTransaction 扫描交易行
func (m *MySQLStore) scanTransaction(rows *sql.Rows) (core.UnifiedTransaction, error) {
	var tx core.UnifiedTransaction
	var rawDataJSON sql.NullString

	err := rows.Scan(
		&tx.TxHash,
		&tx.ChainType,
		&tx.ChainID,
		utils.NullInt64ToBigInt(&tx.BlockNumber),
		utils.NullStringToString(&tx.BlockHash),
		utils.NullIntToInt(&tx.TxIndex),
		utils.NullStringToString(&tx.FromAddress),
		utils.NullStringToString(&tx.ToAddress),
		utils.NullStringToBigInt(&tx.Value),
		utils.NullInt64ToBigInt(&tx.GasLimit),
		utils.NullInt64ToBigInt(&tx.GasUsed),
		utils.NullInt64ToBigInt(&tx.GasPrice),
		&tx.Status,
		&tx.Timestamp,
		&rawDataJSON,
	)
	if err != nil {
		return tx, err
	}

	// 解析原始数据
	if rawDataJSON.Valid {
		if err := utils.FromJSON(rawDataJSON.String, &tx.RawData); err != nil {
			log.Printf("⚠️ 解析原始数据失败: %v", err)
		}
	}

	return tx, nil
}

// scanBusinessEvent 扫描业务事件行
func (m *MySQLStore) scanBusinessEvent(rows *sql.Rows) (core.BusinessEvent, error) {
	var event core.BusinessEvent
	var eventDataJSON sql.NullString

	err := rows.Scan(
		&event.EventID,
		&event.EventType,
		&event.Protocol,
		&event.TxHash,
		&event.ChainType,
		&eventDataJSON,
		&event.Timestamp,
	)
	if err != nil {
		return event, err
	}

	// 解析事件数据
	if eventDataJSON.Valid {
		if err := utils.FromJSON(eventDataJSON.String, &event.Data); err != nil {
			log.Printf("⚠️ 解析事件数据失败: %v", err)
		}
	}

	return event, nil
}
