package core

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"
	"sync"
	"time"
	"unified-tx-parser/pkg/model"
)

// SuiProcessorInjectable 可注入Sui处理器的接口
type SuiProcessorInjectable interface {
	SetSuiProcessor(processor interface{})
}

// Engine 统一交易处理引擎
type Engine struct {
	// 链处理器
	chainProcessors map[ChainType]ChainProcessor

	// DEX数据提取器
	dexExtractors []DexExtractors

	// 存储引擎
	storage StorageEngine

	// 进度跟踪器
	progressTracker ProgressTracker

	// 配置
	config *EngineConfig

	// 运行状态
	running bool
	mu      sync.RWMutex

	// 取消上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// EngineConfig 引擎配置
type EngineConfig struct {
	// 批处理大小
	BatchSize int `json:"batch_size"`

	// 处理间隔
	ProcessInterval time.Duration `json:"process_interval"`

	// 最大重试次数
	MaxRetries int `json:"max_retries"`

	// 并发处理数
	ConcurrentChains int `json:"concurrent_chains"`

	// 是否启用实时模式
	RealTimeMode bool `json:"real_time_mode"`

	// 链特定配置
	ChainConfigs map[ChainType]*ChainConfig `json:"chain_configs"`
}

// ChainConfig 链配置
type ChainConfig struct {
	Enabled     bool   `json:"enabled"`
	StartBlock  int64  `json:"start_block"`
	BatchSize   int    `json:"batch_size"`
	RpcEndpoint string `json:"rpc_endpoint"`
}

// NewEngine 创建新的处理引擎
func NewEngine(config *EngineConfig) *Engine {
	ctx, cancel := context.WithCancel(context.Background())

	if config == nil {
		config = &EngineConfig{
			BatchSize:        100,
			ProcessInterval:  time.Second * 10,
			MaxRetries:       3,
			ConcurrentChains: 4,
			RealTimeMode:     true,
		}
	}

	return &Engine{
		chainProcessors: make(map[ChainType]ChainProcessor),
		dexExtractors:   make([]DexExtractors, 0),
		config:          config,
		ctx:             ctx,
		cancel:          cancel,
	}
}

// RegisterChainProcessor 注册链处理器
func (e *Engine) RegisterChainProcessor(processor ChainProcessor) {
	e.mu.Lock()
	defer e.mu.Unlock()

	chainType := processor.GetChainType()
	e.chainProcessors[chainType] = processor

	// 为已注册的DEX提取器注入链处理器
	e.injectChainProcessorToExtractors(chainType, processor)

	log.Printf("🔗 注册链处理器: %s", chainType)
}

// RegisterDexExtractor 注册DEX数据提取器
func (e *Engine) RegisterDexExtractor(extractor DexExtractors) {
	e.mu.Lock()
	defer e.mu.Unlock()

	// 检查是否需要注入链处理器
	e.injectChainProcessors(extractor)

	e.dexExtractors = append(e.dexExtractors, extractor)

	protocols := extractor.GetSupportedProtocols()
	chains := extractor.GetSupportedChains()
	log.Printf("📦 注册DEX数据提取器: 协议=%v, 链=%v", protocols, chains)
}

// SetStorageEngine 设置存储引擎
func (e *Engine) SetStorageEngine(storage StorageEngine) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.storage = storage
	// 移除冗余日志
}

// SetProgressTracker 设置进度跟踪器
func (e *Engine) SetProgressTracker(tracker ProgressTracker) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.progressTracker = tracker
	log.Printf("📋 设置进度跟踪器")
}

// Start 启动引擎
func (e *Engine) Start() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.running {
		return fmt.Errorf("引擎已经在运行")
	}

	if e.storage == nil {
		return fmt.Errorf("未设置存储引擎")
	}

	if len(e.chainProcessors) == 0 {
		return fmt.Errorf("未注册任何链处理器")
	}

	e.running = true

	// 启动每个链的处理协程
	for chainType, processor := range e.chainProcessors {
		if config, exists := e.config.ChainConfigs[chainType]; exists && !config.Enabled {
			log.Printf("⚠️ 链 %s 已禁用", chainType)
			continue
		}

		go e.processChain(chainType, processor)
		log.Printf("🚀 启动链处理器: %s", chainType)
	}

	log.Printf("🚀 统一交易处理引擎已启动")
	return nil
}

// Stop 停止引擎
func (e *Engine) Stop() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !e.running {
		return
	}

	e.running = false
	e.cancel()

	log.Printf("🛑 统一交易处理引擎已停止")
}

// IsRunning 检查引擎是否在运行
func (e *Engine) IsRunning() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.running
}

// processChain 处理单个链的数据
func (e *Engine) processChain(chainType ChainType, processor ChainProcessor) {
	ticker := time.NewTicker(e.config.ProcessInterval)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			log.Printf("🛑 链 %s 处理器停止", chainType)
			return
		case <-ticker.C:
			if err := e.processChainBatch(chainType, processor); err != nil {
				log.Printf("❌ 处理链 %s 时出错: %v", chainType, err)
			}
		}
	}
}

// processChainBatch 处理链的一批数据
func (e *Engine) processChainBatch(chainType ChainType, processor ChainProcessor) error {
	// 获取当前进度
	progress, err := e.getOrCreateProgress(chainType)
	if err != nil {
		return fmt.Errorf("获取进度失败: %w", err)
	}

	// 获取最新区块号
	latestBlock, err := processor.GetLatestBlockNumber(e.ctx)
	if err != nil {
		return fmt.Errorf("获取最新区块号失败: %w", err)
	}

	// 计算处理范围
	startBlock := progress.LastProcessedBlock

	if startBlock.Cmp(big.NewInt(0)) == 0 {
		// 如果没有处理过，从配置的起始块开始
		if config, exists := e.config.ChainConfigs[chainType]; exists && config.StartBlock > 0 {
			startBlock = big.NewInt(config.StartBlock)
		} else {
			startBlock = big.NewInt(0).Sub(latestBlock, big.NewInt(100)) // 默认从最新块往前100块开始
		}
	} else {
		// 从下一个区块开始处理（避免重复处理）
		startBlock = big.NewInt(0).Add(startBlock, big.NewInt(1))
	}

	// 确定批处理大小
	batchSize := e.config.BatchSize
	if config, exists := e.config.ChainConfigs[chainType]; exists && config.BatchSize > 0 {
		batchSize = config.BatchSize
	}

	endBlock := big.NewInt(0).Add(startBlock, big.NewInt(int64(batchSize)))
	if endBlock.Cmp(latestBlock) > 0 {
		endBlock = latestBlock
	}

	// 简化的处理范围日志
	log.Printf("📊 [%s] 区块 %s-%s", strings.ToUpper(string(chainType)), startBlock.String(), endBlock.String())

	// 如果没有新块需要处理
	if startBlock.Cmp(endBlock) >= 0 {
		return nil
	}

	log.Printf("🔄 处理链 %s: 块 %s 到 %s", chainType, startBlock.String(), endBlock.String())

	// 获取交易数据 - 添加超时控制
	txCtx, txCancel := context.WithTimeout(e.ctx, time.Minute*5) // 5分钟超时
	defer txCancel()

	log.Printf("⏳ 开始获取区块数据...")
	startTime := time.Now()

	blocks, err := processor.GetBlocksByRange(txCtx, startBlock, endBlock)
	if err != nil {
		return fmt.Errorf("获取区块数据失败: %w", err)
	}

	duration := time.Since(startTime)
	// 简化日志：只在有区块时输出
	if len(blocks) > 0 {
		totalTxs := 0
		for _, block := range blocks {
			totalTxs += len(block.Transactions)
		}
		log.Printf("📥 [%s] %d个区块, %d笔交易 (%.1fs)", strings.ToUpper(string(chainType)), len(blocks), totalTxs, duration.Seconds())
	}
	// 如果没有区块数据，直接返回
	if len(blocks) == 0 {
		return nil
	}

	// 提取DEX数据
	log.Printf("⏳ 开始提取DEX数据...")
	extractStartTime := time.Now()

	dexData, err := e.extractDexData(blocks)
	if err != nil {
		return fmt.Errorf("提取DEX数据失败: %w", err)
	}

	extractDuration := time.Since(extractStartTime)
	totalDexRecords := len(dexData.Pools) + len(dexData.Transactions) + len(dexData.Liquidities) + len(dexData.Reserves) + len(dexData.Tokens)

	if totalDexRecords > 0 {
		log.Printf("📊 [%s] DEX数据提取完成: %d条记录 (%.1fs)",
			strings.ToUpper(string(chainType)), totalDexRecords, extractDuration.Seconds())
		log.Printf("   - 池子: %d, 交易: %d, 流动性: %d, 储备: %d, 代币: %d",
			len(dexData.Pools), len(dexData.Transactions), len(dexData.Liquidities),
			len(dexData.Reserves), len(dexData.Tokens))
	}

	// 存储数据
	log.Printf("⏳ 开始存储数据...")
	storeStartTime := time.Now()

	// // 存储区块数据
	// if len(blocks) > 0 {
	// 	if err := e.storage.StoreBlocks(e.ctx, blocks); err != nil {
	// 		return fmt.Errorf("存储区块失败: %w", err)
	// 	}
	// }

	// 存储DEX数据
	if totalDexRecords > 0 {
		if err := e.storage.StoreDexData(e.ctx, dexData); err != nil {
			return fmt.Errorf("存储DEX数据失败: %w", err)
		}
	}

	storeDuration := time.Since(storeStartTime)
	if totalDexRecords > 0 {
		log.Printf("💾 [%s] 存储完成: %d条DEX记录 (%.1fs)",
			strings.ToUpper(string(chainType)), totalDexRecords, storeDuration.Seconds())
	}

	// 更新进度
	progress.LastProcessedBlock = endBlock
	progress.LastUpdateTime = time.Now()

	// 计算总交易数
	totalTxs := 0
	for _, block := range blocks {
		totalTxs += len(block.Transactions)
	}

	progress.TotalTransactions += int64(totalTxs)
	progress.TotalEvents += int64(totalDexRecords)

	if e.progressTracker != nil {
		if err := e.progressTracker.UpdateProgress(chainType, progress); err != nil {
			log.Printf("⚠️ %s: 进度保存失败 - %v", chainType, err)
		}
	}

	// 完成日志
	if len(blocks) > 0 || totalDexRecords > 0 {
		log.Printf("✅ [%s] 处理完成: %d区块, %d交易, %d条DEX记录 (区块:%s)",
			strings.ToUpper(string(chainType)), len(blocks), totalTxs, totalDexRecords, endBlock.String())
	}

	return nil
}

// extractDexData 从区块中提取DEX数据
func (e *Engine) extractDexData(blocks []UnifiedBlock) (*DexData, error) {
	// 创建一个空的DexData结构
	result := &DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有DEX提取器
	for _, extractor := range e.dexExtractors {
		// 检查是否有支持的区块
		supportedBlocks := make([]UnifiedBlock, 0)
		for _, block := range blocks {
			if extractor.SupportsBlock(&block) {
				supportedBlocks = append(supportedBlocks, block)
			}
		}

		if len(supportedBlocks) == 0 {
			continue
		}

		// 提取DEX数据
		dexData, err := extractor.ExtractDexData(e.ctx, supportedBlocks)
		if err != nil {
			return nil, fmt.Errorf("DEX提取器提取数据失败: %w", err)
		}

		// 合并数据
		result.Pools = append(result.Pools, dexData.Pools...)
		result.Transactions = append(result.Transactions, dexData.Transactions...)
		result.Liquidities = append(result.Liquidities, dexData.Liquidities...)
		result.Reserves = append(result.Reserves, dexData.Reserves...)
		result.Tokens = append(result.Tokens, dexData.Tokens...)
	}

	return result, nil
}

// getOrCreateProgress 获取或创建处理进度
func (e *Engine) getOrCreateProgress(chainType ChainType) (*ProcessProgress, error) {
	if e.progressTracker == nil {
		log.Printf("⚠️ 链 %s 没有进度跟踪器，使用默认进度", chainType)
		return &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
		}, nil
	}

	progress, err := e.progressTracker.GetProgress(chainType)
	if err != nil {
		log.Printf("⚠️ 链 %s 获取进度失败: %v，创建新进度", chainType, err)
		// 如果没有找到进度记录，创建新的
		progress = &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
		}
	} else {
		log.Printf("✅ 链 %s 成功获取进度: LastProcessedBlock=%s", chainType, progress.LastProcessedBlock.String())
	}

	return progress, nil
}

// GetStats 获取引擎统计信息
func (e *Engine) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	e.mu.RLock()
	stats["running"] = e.running
	stats["registered_chains"] = len(e.chainProcessors)
	stats["registered_extractors"] = len(e.dexExtractors)
	e.mu.RUnlock()

	// 获取存储统计
	if e.storage != nil {
		storageStats, err := e.storage.GetStorageStats(ctx)
		if err == nil {
			stats["storage"] = storageStats
		}
	}

	// 获取链进度
	if e.progressTracker != nil {
		chainProgress := make(map[string]*ProcessProgress)
		for chainType := range e.chainProcessors {
			if progress, err := e.progressTracker.GetProgress(chainType); err == nil {
				chainProgress[string(chainType)] = progress
			}
		}
		stats["chain_progress"] = chainProgress
	}

	return stats, nil
}

// injectChainProcessors 为DEX提取器注入对应的链处理器
func (e *Engine) injectChainProcessors(extractor DexExtractors) {
	// 检查提取器支持的链类型
	supportedChains := extractor.GetSupportedChains()

	for _, chainType := range supportedChains {
		// 获取对应的链处理器
		if processor, exists := e.chainProcessors[chainType]; exists {
			// 检查是否是可注入Sui处理器的提取器
			if injectable, ok := extractor.(SuiProcessorInjectable); ok && chainType == ChainTypeSui {
				injectable.SetSuiProcessor(processor)
				log.Printf("🔗 为DEX提取器注入%s链处理器", chainType)
			}
		}
	}
}

// injectChainProcessorToExtractors 为已注册的DEX提取器注入新注册的链处理器
func (e *Engine) injectChainProcessorToExtractors(chainType ChainType, processor ChainProcessor) {
	for _, extractor := range e.dexExtractors {
		// 检查提取器是否支持该链类型
		supportedChains := extractor.GetSupportedChains()
		for _, supportedChain := range supportedChains {
			if supportedChain == chainType {
				// 检查是否是可注入Sui处理器的提取器
				if injectable, ok := extractor.(SuiProcessorInjectable); ok && chainType == ChainTypeSui {
					injectable.SetSuiProcessor(processor)
					log.Printf("🔗 为已注册的DEX提取器注入%s链处理器", chainType)
				}
				break
			}
		}
	}
}
