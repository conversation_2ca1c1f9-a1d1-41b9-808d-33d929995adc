package dex

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"sync"
	"time"

	"unified-tx-parser/pkg/chains/sui"
	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/model"
	"unified-tx-parser/pkg/utils"

	"github.com/block-vision/sui-go-sdk/models"
)

// Bluefin协议常量
const (
	// Bluefin AMM合约地址
	bluefinAmmAddr = "0x3492c874c1e3b3e2984e8c41b589e642d4d0a5d6459e5a9cfc2d52fd7c89c267"

	// Bluefin事件类型
	bluefinPoolCreatedEventType     = bluefinAmmAddr + "::events::PoolCreated"
	bluefinAddLiquidityEventType    = bluefinAmmAddr + "::events::LiquidityProvided"
	bluefinRemoveLiquidityEventType = bluefinAmmAddr + "::events::LiquidityRemoved"
	bluefinAssetSwapEventType       = bluefinAmmAddr + "::events::AssetSwap"
	bluefinFlashSwapEventType       = bluefinAmmAddr + "::events::FlashSwap"
)

// TokenCacheItem 代币缓存项
type TokenCacheItem struct {
	Token     model.Token
	ExpiresAt time.Time
}

// BluefinExtractor Bluefin DEX数据提取器 - 简化的自包含实现
type BluefinExtractor struct {
	client     *sui.SuiProcessor
	tokenCache map[string]*TokenCacheItem // 代币元数据缓存
	cacheMutex sync.RWMutex               // 缓存读写锁
	// 移除handler依赖，直接在此类中实现所有逻辑
}

// NewBluefinExtractor 创建Bluefin提取器
func NewBluefinExtractor() *BluefinExtractor {
	return &BluefinExtractor{
		tokenCache: make(map[string]*TokenCacheItem),
	}
}

// SetSuiProcessor 设置Sui处理器（用于获取链上数据）
func (b *BluefinExtractor) SetSuiProcessor(processor interface{}) {
	if suiProcessor, ok := processor.(*sui.SuiProcessor); ok {
		b.client = suiProcessor
	}
}

// GetSupportedProtocols 获取支持的协议
func (b *BluefinExtractor) GetSupportedProtocols() []string {
	return []string{"bluefin"}
}

// GetSupportedChains 获取支持的链类型
func (b *BluefinExtractor) GetSupportedChains() []core.ChainType {
	return []core.ChainType{core.ChainTypeSui}
}

// ExtractDexData 从统一区块数据中提取Bluefin DEX相关数据 - 简化实现
func (b *BluefinExtractor) ExtractDexData(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	// 检查是否设置了Sui处理器
	if b.client == nil {
		return nil, fmt.Errorf("Sui处理器未设置，请先调用SetSuiProcessor方法")
	}

	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有区块
	for _, block := range blocks {
		// 只处理Sui链的区块
		if block.ChainType != core.ChainTypeSui {
			continue
		}

		// 遍历区块中的所有交易
		for _, tx := range block.Transactions {
			// 直接从交易的原始数据中提取Sui事件
			suiEvents := b.extractSuiEventsFromTransaction(&tx)
			if len(suiEvents) == 0 {
				continue
			}

			// 处理每个事件
			for _, event := range suiEvents {
				if !b.isBluefinEvent(event) {
					continue
				}

				// 根据事件类型处理
				eventType := b.getEventType(event)
				switch eventType {
				case "swap":
					swapData := b.processSwapEvent(ctx, event, &tx, &block)
					if swapData != nil {
						dexData.Pools = append(dexData.Pools, swapData.Pool)
						dexData.Tokens = append(dexData.Tokens, swapData.Tokens...)
						dexData.Reserves = append(dexData.Reserves, swapData.Reserve)
						dexData.Transactions = append(dexData.Transactions, swapData.Transactions...)
					}

				case "add_liquidity":
					if liquidityData := b.createLiquidityFromEvent(ctx, event, &tx, &block, "add"); liquidityData != nil {
						dexData.Pools = append(dexData.Pools, liquidityData.Pool)
						dexData.Tokens = append(dexData.Tokens, liquidityData.Tokens...)
						dexData.Reserves = append(dexData.Reserves, liquidityData.Reserve)
						dexData.Liquidities = append(dexData.Liquidities, liquidityData.Liquidities...)
					}

				case "remove_liquidity":
					if liquidityData := b.createLiquidityFromEvent(ctx, event, &tx, &block, "remove"); liquidityData != nil {
						dexData.Pools = append(dexData.Pools, liquidityData.Pool)
						dexData.Tokens = append(dexData.Tokens, liquidityData.Tokens...)
						dexData.Reserves = append(dexData.Reserves, liquidityData.Reserve)
						dexData.Liquidities = append(dexData.Liquidities, liquidityData.Liquidities...)
					}

				case "pool_created":
					if pool := b.createPoolFromEvent(ctx, event, &tx); pool != nil {
						dexData.Pools = append(dexData.Pools, *pool)
					}
				}
			}
		}
	}

	return dexData, nil
}

const shortCoinType = "0x2::sui::SUI"

// SwapEventData 交换事件处理结果
type SwapEventData struct {
	Pool         model.Pool
	Tokens       []model.Token
	Reserve      model.Reserve
	Transactions []model.Transaction
}

// LiquidityEventData 流动性事件处理结果
type LiquidityEventData struct {
	Pool        model.Pool
	Tokens      []model.Token
	Reserve     model.Reserve
	Liquidities []model.Liquidity
}

// PoolEventData 池子事件的通用数据结构
type PoolEventData struct {
	Pool    model.Pool
	Tokens  []model.Token
	Reserve model.Reserve
}

// ExtractPoolCoin 获取pool池里面代币地址
func (b *BluefinExtractor) ExtractPoolCoin(coinType string) (string, string) {
	token0, token1 := utils.ExtractPoolTokens(coinType)

	// 特殊处理SUI代币
	if strings.EqualFold(token0, shortCoinType) {
		token0 = shortCoinType
	}
	if strings.EqualFold(token1, shortCoinType) {
		token1 = shortCoinType
	}

	return token0, token1
}

// processSwapEvent 处理交换事件 - 重点关注AssetSwap事件的特定字段
func (b *BluefinExtractor) processSwapEvent(ctx context.Context, event map[string]interface{}, tx *core.UnifiedTransaction, block *core.UnifiedBlock) *SwapEventData {
	// 提取事件基本信息
	eventSeq := b.extractEventSeq(event)
	sender := b.extractSender(event)
	// eventType := b.extractEventType(event)

	//fmt.Printf("🐟 [Bluefin AssetSwap] eventSeq: %s, sender: %s, type: %s\n", eventSeq, sender, eventType)

	// 提取parsedJson中的关键字段
	parsedJson := event["parsedJson"]
	if parsedJson == nil {
		fmt.Println("🐟 [Bluefin AssetSwap] parsedJson is nil")
		return nil
	}

	fields, ok := parsedJson.(map[string]interface{})
	if !ok {
		fmt.Println("🐟 [Bluefin AssetSwap] parsedJson is not a map")
		return nil
	}

	// 提取关键字段
	poolId := b.getStringField(fields, "pool_id")
	amountIn := b.getBigIntField(fields, "amount_in")
	amountOut := b.getBigIntField(fields, "amount_out")

	// fmt.Printf("🐟 [Bluefin AssetSwap] pool_id: %s, amount_in: %v, amount_out: %v\n", poolId, amountIn, amountOut)

	// 使用公共方法获取池子数据
	poolEventData, err := b.getPoolEventData(ctx, poolId, tx, block)
	if err != nil {
		fmt.Printf("🐟 [Bluefin AssetSwap] Failed to get pool event data: %v\n", err)
		return nil
	}

	// 获取token地址
	token0 := poolEventData.Pool.Tokens[0]
	token1 := poolEventData.Pool.Tokens[1]

	// 构建返回数据
	return &SwapEventData{
		Pool:    poolEventData.Pool,
		Tokens:  poolEventData.Tokens,
		Reserve: poolEventData.Reserve,
		Transactions: []model.Transaction{
			{
				Addr:        token0,
				Factory:     bluefinAmmAddr,
				Pool:        poolId,
				Hash:        tx.TxHash,
				EventIndex:  b.parseEventSeq(eventSeq),
				TxIndex:     int64(tx.TxIndex),
				BlockNumber: b.getBlockNumber(tx),
				Time:        uint64(block.Timestamp.Unix()),
				From:        sender, // event.sender
				Side:        "sell",
				Amount:      amountIn,
				Price:       0, // TODO: 计算价格
				Value:       0, // TODO: 计算Value
			},
			{
				Addr:        token1,
				Factory:     bluefinAmmAddr,
				Pool:        poolId,
				Hash:        tx.TxHash,
				EventIndex:  b.parseEventSeq(eventSeq),
				TxIndex:     int64(tx.TxIndex),
				BlockNumber: b.getBlockNumber(tx),
				Time:        uint64(block.Timestamp.Unix()),
				From:        sender, // event.sender
				Side:        "buy",
				Amount:      amountOut,
				Price:       0, // TODO: 计算价格
				Value:       0, // TODO: 计算Value
			},
		},
	}
}

// token0Reserve token1Reserve
func (b *BluefinExtractor) getPoolTokenBalances(token0, token1 string, poolObject models.SuiObjectResponse) (*big.Int, *big.Int, error) {
	balanceCoinA := poolObject.Data.Content.Fields["coin_a"].(string)
	balanceCoinB := poolObject.Data.Content.Fields["coin_b"].(string)
	if balanceCoinA == "" || balanceCoinB == "" {
		return &big.Int{}, &big.Int{}, fmt.Errorf("无法获取池子代币余额")
	}
	balanceCoinAValue, ok := new(big.Int).SetString(balanceCoinA, 10)
	if !ok {
		return &big.Int{}, &big.Int{}, fmt.Errorf("无法解析池子代币余额")
	}
	balanceCoinBValue, ok := new(big.Int).SetString(balanceCoinB, 10)
	if !ok {
		return &big.Int{}, &big.Int{}, fmt.Errorf("无法解析池子代币余额")
	}
	return balanceCoinAValue, balanceCoinBValue, nil
}

// SupportsBlock 检查是否支持该区块 - 简化实现
func (b *BluefinExtractor) SupportsBlock(block *core.UnifiedBlock) bool {
	// 只支持Sui链
	if block.ChainType != core.ChainTypeSui {
		return false
	}

	// 检查区块中是否有任何交易包含Bluefin事件
	for _, tx := range block.Transactions {
		suiEvents := b.extractSuiEventsFromTransaction(&tx)
		for _, event := range suiEvents {
			if b.isBluefinEvent(event) {
				return true
			}
		}
	}
	return false
}

// extractSuiEventsFromTransaction 从交易中提取Sui事件
func (b *BluefinExtractor) extractSuiEventsFromTransaction(tx *core.UnifiedTransaction) []map[string]interface{} {
	// 根据不同的原始数据类型处理
	switch rawData := tx.RawData.(type) {
	case map[string]interface{}:
		// 如果是map格式，尝试获取events字段
		if events, ok := rawData["events"]; ok {
			return b.parseEventsFromInterface(events)
		}
	default:
		// 尝试通过JSON解析
		data, err := json.Marshal(rawData)
		if err != nil {
			return nil
		}

		var result map[string]interface{}
		if err := json.Unmarshal(data, &result); err != nil {
			return nil
		}

		if events, ok := result["events"]; ok {
			return b.parseEventsFromInterface(events)
		}
	}

	return nil
}

// parseEventsFromInterface 解析事件接口
func (b *BluefinExtractor) parseEventsFromInterface(events interface{}) []map[string]interface{} {
	return utils.ParseEventsFromInterface(events)
}

// isBluefinEvent 检查是否是Bluefin事件
func (b *BluefinExtractor) isBluefinEvent(event map[string]interface{}) bool {
	eventType, ok := event["type"].(string)
	if !ok {
		return false
	}

	// 检查是否包含Bluefin合约地址
	return eventType == bluefinPoolCreatedEventType ||
		eventType == bluefinAddLiquidityEventType ||
		eventType == bluefinRemoveLiquidityEventType ||
		eventType == bluefinAssetSwapEventType ||
		eventType == bluefinFlashSwapEventType
}

// getEventType 获取事件类型
func (b *BluefinExtractor) getEventType(event map[string]interface{}) string {
	eventType, ok := event["type"].(string)
	if !ok {
		return ""
	}

	switch eventType {
	case bluefinAssetSwapEventType, bluefinFlashSwapEventType:
		return "swap"
	case bluefinAddLiquidityEventType:
		return "add_liquidity"
	case bluefinRemoveLiquidityEventType:
		return "remove_liquidity"
	case bluefinPoolCreatedEventType:
		return "pool_created"
	default:
		return ""
	}
}

// createLiquidityFromEvent 从事件创建流动性记录 - 重点关注LiquidityRemoved事件的特定字段
func (b *BluefinExtractor) createLiquidityFromEvent(ctx context.Context, event map[string]interface{}, tx *core.UnifiedTransaction, block *core.UnifiedBlock, side string) *LiquidityEventData {
	// 提取事件基本信息
	eventSeq := b.extractEventSeq(event)
	sender := b.extractSender(event)
	//eventType := b.extractEventType(event)

	//fmt.Printf("🐟 [Bluefin Liquidity] eventSeq: %s, sender: %s, type: %s, side: %s\n", eventSeq, sender, eventType, side)

	parsedFields := event["parsedJson"]
	if parsedFields == nil {
		fmt.Println("🐟 [Bluefin Liquidity] parsedJson is nil")
		return nil
	}

	fields, ok := parsedFields.(map[string]interface{})
	if !ok {
		fmt.Println("🐟 [Bluefin Liquidity] parsedJson is not a map")
		return nil
	}

	// 提取关键字段
	poolAddr := b.getStringField(fields, "pool_id")
	if poolAddr == "" {
		fmt.Println("🐟 [Bluefin Liquidity] pool_id is empty")
		return nil
	}

	// 使用公共方法获取池子数据
	poolEventData, err := b.getPoolEventData(ctx, poolAddr, tx, block)
	if err != nil {
		fmt.Printf("🐟 [Bluefin Liquidity] Failed to get pool event data: %v\n", err)
		return nil
	}

	// 获取token地址
	token0 := poolEventData.Pool.Tokens[0]
	token1 := poolEventData.Pool.Tokens[1]

	// 对于LiquidityRemoved事件，重点关注coin_a_amount和coin_b_amount
	var coinAAmount, coinBAmount *big.Int
	// LiquidityRemoved事件的特定字段
	coinAAmount = b.getBigIntField(fields, "coin_a_amount")
	coinBAmount = b.getBigIntField(fields, "coin_b_amount")

	// 构建流动性记录
	liquidity1 := model.Liquidity{
		Addr:    token0,
		Router:  poolAddr,
		Factory: bluefinAmmAddr,
		Pool:    poolAddr,
		Hash:    tx.TxHash,
		From:    sender, // 使用事件中的sender而不是tx.FromAddress
		Side:    side,
		Amount:  coinAAmount,
		Value:   0, // u的价值，需要先处理decimals,然后*price
		Time:    uint64(block.Timestamp.Unix()),
		Key:     tx.TxHash + "_" + side + "_" + eventSeq,
	}
	liquidity2 := model.Liquidity{
		Addr:    token1,
		Router:  poolAddr,
		Factory: bluefinAmmAddr,
		Pool:    poolAddr,
		Hash:    tx.TxHash,
		From:    sender, // 使用事件中的sender而不是tx.FromAddress
		Side:    side,
		Amount:  coinBAmount,
		Value:   0, // u的价值，需要先处理decimals,然后*price
		Time:    uint64(block.Timestamp.Unix()),
		Key:     tx.TxHash + "_" + side + "_" + eventSeq,
	}

	// 构建返回数据
	return &LiquidityEventData{
		Pool:        poolEventData.Pool,
		Tokens:      poolEventData.Tokens,
		Reserve:     poolEventData.Reserve,
		Liquidities: []model.Liquidity{liquidity1, liquidity2},
	}
}

// createPoolFromEvent 从事件创建池子记录
func (b *BluefinExtractor) createPoolFromEvent(ctx context.Context, event map[string]interface{}, tx *core.UnifiedTransaction) *model.Pool {
	parsedFields := event["parsedJson"]
	if parsedFields == nil {
		return nil
	}

	fields, ok := parsedFields.(map[string]interface{})
	if !ok {
		return nil
	}

	poolAddr := b.getStringField(fields, "pool_id")
	if poolAddr == "" {
		return nil
	}
	// 获取池对象
	poolObject, err := b.getPoolObject(ctx, poolAddr)
	if err != nil {
		fmt.Printf("🐟 [Bluefin AssetSwap] Failed to get pool object for %s: %v\n", poolAddr, err)
		return nil
	}

	// 提取token地址
	token0, token1 := b.ExtractPoolCoin(poolObject.Data.Type)
	if token0 == "" || token1 == "" {
		fmt.Printf("🐟 [Bluefin AssetSwap] Failed to extract token addresses from pool type: %s\n", poolObject.Data.Type)
		return nil
	}

	return &model.Pool{
		Addr:     poolAddr,
		Factory:  bluefinAmmAddr,
		Protocol: "bluefin",
		Tokens: map[int]string{
			0: token0,
			1: token1,
		},
	}
}

// getStringField 安全获取字符串字段
func (b *BluefinExtractor) getStringField(fields map[string]interface{}, key string) string {
	return utils.GetStringField(fields, key)
}

// getBigIntField 安全获取大整数字段
func (b *BluefinExtractor) getBigIntField(fields map[string]interface{}, key string) *big.Int {
	return utils.GetBigIntField(fields, key)
}

// extractPoolIdFromEvent 从事件中提取pool_id
func (b *BluefinExtractor) extractPoolIdFromEvent(event map[string]interface{}) string {
	parsedJson := event["parsedJson"]
	if parsedJson == nil {
		fmt.Println("parsedJson is nil")
		return ""
	}

	fields, ok := parsedJson.(map[string]interface{})
	if !ok {
		fmt.Println("parsedJson is not a map")
		return ""
	}

	poolId, ok := fields["pool_id"].(string)
	if !ok {
		fmt.Println("pool_id not found or not a string:", fields["pool_id"])
		return ""
	}

	//fmt.Println("Found poolId:", poolId)
	return poolId
}

// extractEventSeq 从事件中提取eventSeq
func (b *BluefinExtractor) extractEventSeq(event map[string]interface{}) string {
	if id, ok := event["id"].(map[string]interface{}); ok {
		if eventSeq, ok := id["eventSeq"].(string); ok {
			return eventSeq
		}
	}
	return ""
}

// extractSender 从事件中提取sender
func (b *BluefinExtractor) extractSender(event map[string]interface{}) string {
	if sender, ok := event["sender"].(string); ok {
		return sender
	}
	return ""
}

// extractEventType 从事件中提取type
func (b *BluefinExtractor) extractEventType(event map[string]interface{}) string {
	if eventType, ok := event["type"].(string); ok {
		return eventType
	}
	return ""
}

// parseEventSeq 将eventSeq字符串转换为整数
func (b *BluefinExtractor) parseEventSeq(eventSeq string) int64 {
	if eventSeq == "" {
		return 0
	}
	if val, err := strconv.ParseInt(eventSeq, 10, 64); err == nil {
		return val
	}
	return 0
}

// getBlockNumber 安全获取区块号
func (b *BluefinExtractor) getBlockNumber(tx *core.UnifiedTransaction) int64 {
	if tx.BlockNumber != nil {
		return tx.BlockNumber.Int64()
	}
	return 0
}

// getPoolObject 获取池对象
func (b *BluefinExtractor) getPoolObject(ctx context.Context, poolId string) (models.SuiObjectResponse, error) {
	return b.client.GetObject(ctx, models.SuiGetObjectRequest{
		ObjectId: poolId,
		Options: models.SuiObjectDataOptions{
			ShowType:                true,
			ShowContent:             true,
			ShowBcs:                 false,
			ShowOwner:               false,
			ShowPreviousTransaction: false,
			ShowStorageRebate:       false,
			ShowDisplay:             false,
		},
	})
}

// getTokensMetadata 获取token元数据
func (b *BluefinExtractor) getTokensMetadata(ctx context.Context, token0, token1 string) (model.Token, model.Token, error) {
	token0Metadata, err := b.client.GetToken(ctx, models.SuiXGetCoinMetadataRequest{
		CoinType: token0,
	})
	if err != nil {
		return model.Token{}, model.Token{}, fmt.Errorf("failed to get token0 metadata: %w", err)
	}

	token1Metadata, err := b.client.GetToken(ctx, models.SuiXGetCoinMetadataRequest{
		CoinType: token1,
	})
	if err != nil {
		return model.Token{}, model.Token{}, fmt.Errorf("failed to get token1 metadata: %w", err)
	}

	return model.Token{
			Addr:     token0,
			Symbol:   token0Metadata.Symbol,
			Decimals: token0Metadata.Decimals,
			Name:     token0Metadata.Name,
		},
		model.Token{
			Addr:     token1,
			Symbol:   token1Metadata.Symbol,
			Decimals: token1Metadata.Decimals,
			Name:     token1Metadata.Name,
		}, nil
}

// getPoolEventData 获取池子事件的通用数据（消除重复代码）
func (b *BluefinExtractor) getPoolEventData(ctx context.Context, poolId string, tx *core.UnifiedTransaction, block *core.UnifiedBlock) (*PoolEventData, error) {
	if poolId == "" {
		return nil, fmt.Errorf("pool_id is empty")
	}

	// 获取池对象
	poolObject, err := b.getPoolObject(ctx, poolId)
	if err != nil {
		return nil, fmt.Errorf("failed to get pool object for %s: %w", poolId, err)
	}

	// 提取token地址
	token0, token1 := b.ExtractPoolCoin(poolObject.Data.Type)
	if token0 == "" || token1 == "" {
		return nil, fmt.Errorf("failed to extract token addresses from pool type: %s", poolObject.Data.Type)
	}

	// 获取token储备量
	token0Reserve, token1Reserve, _ := b.getPoolTokenBalances(token0, token1, poolObject)

	// 获取token元数据（使用缓存）
	token0Metadata, token1Metadata, err := b.getTokensMetadataWithCache(ctx, token0, token1)
	if err != nil {
		return nil, fmt.Errorf("failed to get tokens metadata: %w", err)
	}

	return &PoolEventData{
		Pool: model.Pool{
			Addr:     poolId,
			Factory:  bluefinAmmAddr,
			Protocol: "bluefin",
			Tokens: map[int]string{
				0: token0,
				1: token1,
			},
		},
		Tokens: []model.Token{token0Metadata, token1Metadata},
		Reserve: model.Reserve{
			Addr: poolId,
			Amounts: map[int]*big.Int{
				0: token0Reserve,
				1: token1Reserve,
			},
			Time: uint64(block.Timestamp.Unix()),
		},
	}, nil
}

// getTokensMetadataWithCache 获取token元数据（带缓存）
func (b *BluefinExtractor) getTokensMetadataWithCache(ctx context.Context, token0, token1 string) (model.Token, model.Token, error) {
	// 获取token0元数据
	token0Metadata, err := b.getTokenMetadataWithCache(ctx, token0)
	if err != nil {
		return model.Token{}, model.Token{}, fmt.Errorf("failed to get token0 metadata: %w", err)
	}

	// 获取token1元数据
	token1Metadata, err := b.getTokenMetadataWithCache(ctx, token1)
	if err != nil {
		return model.Token{}, model.Token{}, fmt.Errorf("failed to get token1 metadata: %w", err)
	}

	return token0Metadata, token1Metadata, nil
}

// getTokenMetadataWithCache 获取单个token元数据（带缓存）
func (b *BluefinExtractor) getTokenMetadataWithCache(ctx context.Context, tokenAddr string) (model.Token, error) {
	// 先检查缓存
	b.cacheMutex.RLock()
	if item, exists := b.tokenCache[tokenAddr]; exists {
		// 检查是否过期（缓存1小时）
		if time.Now().Before(item.ExpiresAt) {
			b.cacheMutex.RUnlock()
			//fmt.Printf("🚀 [Cache Hit] Token metadata for %s\n", tokenAddr)
			return item.Token, nil
		}
	}
	b.cacheMutex.RUnlock()

	// 缓存未命中或已过期，从链上获取
	// fmt.Printf("🔄 [Cache Miss] Fetching token metadata for %s\n", tokenAddr)
	tokenMetadata, err := b.client.GetToken(ctx, models.SuiXGetCoinMetadataRequest{
		CoinType: tokenAddr,
	})
	if err != nil {
		return model.Token{}, fmt.Errorf("failed to get token metadata: %w", err)
	}

	token := model.Token{
		Addr:     tokenAddr,
		Symbol:   tokenMetadata.Symbol,
		Decimals: tokenMetadata.Decimals,
		Name:     tokenMetadata.Name,
	}

	// 更新缓存
	b.cacheMutex.Lock()
	b.tokenCache[tokenAddr] = &TokenCacheItem{
		Token:     token,
		ExpiresAt: time.Now().Add(1 * time.Hour), // 缓存1小时
	}
	b.cacheMutex.Unlock()

	return token, nil
}
