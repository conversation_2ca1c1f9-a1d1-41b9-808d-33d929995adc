package dex

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/model"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// PancakeSwap协议常量
const (
	// PancakeSwap V2 合约地址
	pancakeSwapV2RouterAddr  = "******************************************"
	pancakeSwapV2FactoryAddr = "******************************************"

	// PancakeSwap V3 合约地址
	pancakeSwapV3RouterAddr  = "******************************************"
	pancakeSwapV3FactoryAddr = "******************************************"

	// PancakeSwap on Ethereum
	pancakeSwapEthV3RouterAddr = "******************************************"

	// 事件签名哈希 (与Uniswap相同)
	pancakeSwapV2EventSig      = "0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822"
	pancakeSwapV3EventSig      = "0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67"
	pancakeMintV2EventSig      = "0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f"
	pancakeBurnV2EventSig      = "0xdccd412f0b1252819cb1fd330b93224ca42612892bb3f4f789976e6d81936496"
	pancakeMintV3EventSig      = "0x7a53080ba414158be7ec69b987b5fb7d07dee101fe85488f0853ae16239d0bde"
	pancakeBurnV3EventSig      = "0x0c396cd989a39f4459b5fa1aed6a9a8dcdbc45908acfd67e028cd568da98982c"
	pancakePairCreatedEventSig = "0x0d3648bd0f6ba80134a33ba9275ac585d9d315f0ad8355cddefde31afa28d0e9"
	pancakePoolCreatedEventSig = "0x783cca1c0412dd0d695e784568c96da2e9c22ff989357a2e8b1d9b2b4e6b7118"
)

// PancakeSwapExtractor PancakeSwap DEX数据提取器 - 简化的自包含实现
type PancakeSwapExtractor struct {
	// 移除handler依赖，直接在此类中实现所有逻辑
}

// NewPancakeSwapExtractor 创建PancakeSwap提取器
func NewPancakeSwapExtractor() *PancakeSwapExtractor {
	return &PancakeSwapExtractor{}
}

// GetSupportedProtocols 获取支持的协议
func (p *PancakeSwapExtractor) GetSupportedProtocols() []string {
	return []string{"pancakeswap", "pancakeswap-v2", "pancakeswap-v3"}
}

// GetSupportedChains 获取支持的链类型
func (p *PancakeSwapExtractor) GetSupportedChains() []core.ChainType {
	return []core.ChainType{core.ChainTypeEthereum, core.ChainTypeBSC}
}

// ExtractDexData 从统一区块数据中提取PancakeSwap DEX相关数据 - 完整实现
func (p *PancakeSwapExtractor) ExtractDexData(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有区块
	for _, block := range blocks {
		// 只处理以太坊和BSC链的区块
		if !p.isSupported(block.ChainType) {
			continue
		}

		log.Printf("🥞 [PancakeSwap] 处理区块 %s，包含 %d 个交易", block.BlockNumber.String(), len(block.Transactions))

		// 遍历区块中的所有交易
		for i, tx := range block.Transactions {
			// 直接从交易的原始数据中提取以太坊日志
			ethLogs := p.extractEthereumLogsFromTransaction(&tx)
			log.Printf("🥞 [PancakeSwap] 交易 %d/%d (hash: %s) 提取到 %d 个日志", i+1, len(block.Transactions), tx.TxHash, len(ethLogs))

			if len(ethLogs) == 0 {
				continue
			}

			// 处理每个日志
			for _, log := range ethLogs {
				if !p.isPancakeSwapLog(log) {
					continue
				}

				// 根据日志类型处理
				logType := p.getLogType(log)
				switch logType {
				case "swap":
					if modelTx := p.createTransactionFromLog(log, &tx); modelTx != nil {
						dexData.Transactions = append(dexData.Transactions, *modelTx)
					}

				case "mint":
					if liquidity := p.createLiquidityFromLog(log, &tx, "add"); liquidity != nil {
						dexData.Liquidities = append(dexData.Liquidities, *liquidity)
					}

				case "burn":
					if liquidity := p.createLiquidityFromLog(log, &tx, "remove"); liquidity != nil {
						dexData.Liquidities = append(dexData.Liquidities, *liquidity)
					}

				case "pair_created", "pool_created":
					if pool := p.createPoolFromLog(log, &tx); pool != nil {
						dexData.Pools = append(dexData.Pools, *pool)
					}
				}
			}
		}
	}

	return dexData, nil
}

// SupportsBlock 检查是否支持该区块 - 完整实现
func (p *PancakeSwapExtractor) SupportsBlock(block *core.UnifiedBlock) bool {
	// 只支持以太坊和BSC链
	if !p.isSupported(block.ChainType) {
		return false
	}

	// 检查区块中是否有任何交易包含PancakeSwap日志
	for _, tx := range block.Transactions {
		ethLogs := p.extractEthereumLogsFromTransaction(&tx)
		for _, log := range ethLogs {
			if p.isPancakeSwapLog(log) {
				return true
			}
		}
	}
	return false
}

// isSupported 检查是否支持该链类型
func (p *PancakeSwapExtractor) isSupported(chainType core.ChainType) bool {
	return chainType == core.ChainTypeEthereum || chainType == core.ChainTypeBSC
}

// extractEthereumLogsFromTransaction 从交易中提取以太坊日志
func (p *PancakeSwapExtractor) extractEthereumLogsFromTransaction(tx *core.UnifiedTransaction) []*types.Log {
	// 根据不同的原始数据类型处理
	switch rawData := tx.RawData.(type) {
	case map[string]interface{}:
		// 首先尝试直接获取logs字段
		if logs, ok := rawData["logs"]; ok {
			return p.parseLogsFromInterface(logs)
		}

		// 如果没有直接的logs字段，尝试从receipt中获取
		if receipt, ok := rawData["receipt"]; ok {
			if ethReceipt, ok := receipt.(*types.Receipt); ok {
				return ethReceipt.Logs
			}
		}
	}
	return nil
}

// parseLogsFromInterface 解析日志接口
func (p *PancakeSwapExtractor) parseLogsFromInterface(logs interface{}) []*types.Log {
	switch logsData := logs.(type) {
	case []interface{}:
		result := make([]*types.Log, 0, len(logsData))
		for _, log := range logsData {
			if logMap, ok := log.(map[string]interface{}); ok {
				if ethLog := p.convertToEthLog(logMap); ethLog != nil {
					result = append(result, ethLog)
				}
			}
		}
		return result
	default:
		return nil
	}
}

// convertToEthLog 将map转换为以太坊日志
func (p *PancakeSwapExtractor) convertToEthLog(logMap map[string]interface{}) *types.Log {
	log := &types.Log{}

	if address, ok := logMap["address"].(string); ok {
		log.Address = common.HexToAddress(address)
	}

	if topics, ok := logMap["topics"].([]interface{}); ok {
		log.Topics = make([]common.Hash, len(topics))
		for i, topic := range topics {
			if topicStr, ok := topic.(string); ok {
				log.Topics[i] = common.HexToHash(topicStr)
			}
		}
	}

	if data, ok := logMap["data"].(string); ok {
		log.Data = common.FromHex(data)
	}

	return log
}

// isPancakeSwapLog 检查是否是PancakeSwap日志
func (p *PancakeSwapExtractor) isPancakeSwapLog(log *types.Log) bool {
	if len(log.Topics) == 0 {
		return false
	}

	// 检查是否是PancakeSwap相关的事件签名
	topic0 := log.Topics[0].Hex()
	return topic0 == pancakeSwapV2EventSig ||
		topic0 == pancakeSwapV3EventSig ||
		topic0 == pancakeMintV2EventSig ||
		topic0 == pancakeBurnV2EventSig ||
		topic0 == pancakeMintV3EventSig ||
		topic0 == pancakeBurnV3EventSig ||
		topic0 == pancakePairCreatedEventSig ||
		topic0 == pancakePoolCreatedEventSig
}

// getLogType 获取日志类型
func (p *PancakeSwapExtractor) getLogType(log *types.Log) string {
	if len(log.Topics) == 0 {
		return ""
	}

	topic0 := log.Topics[0].Hex()
	switch topic0 {
	case pancakeSwapV2EventSig, pancakeSwapV3EventSig:
		return "swap"
	case pancakeMintV2EventSig, pancakeMintV3EventSig:
		return "mint"
	case pancakeBurnV2EventSig, pancakeBurnV3EventSig:
		return "burn"
	case pancakePairCreatedEventSig:
		return "pair_created"
	case pancakePoolCreatedEventSig:
		return "pool_created"
	default:
		return ""
	}
}

// createTransactionFromLog 从日志创建交易记录
func (p *PancakeSwapExtractor) createTransactionFromLog(log *types.Log, tx *core.UnifiedTransaction) *model.Transaction {
	poolAddr := log.Address.Hex()

	// 解析交易金额和代币信息
	amountIn, amountOut, tokenIn, tokenOut := p.parseSwapAmounts(log)

	// 计算价格
	price := p.calculatePrice(amountIn, amountOut)

	// 计算价值（简化为价格 * 输入金额）
	amountInFloat := new(big.Float).SetInt(amountIn)
	value, _ := new(big.Float).Mul(amountInFloat, big.NewFloat(price)).Float64()

	// 安全获取BlockNumber值
	var blockNumber int64
	if tx.BlockNumber != nil {
		blockNumber = tx.BlockNumber.Int64()
	}

	return &model.Transaction{
		Addr:        poolAddr,
		Router:      tx.ToAddress,
		Factory:     p.getFactoryAddress(log),
		Pool:        poolAddr,
		Hash:        tx.TxHash,
		From:        tx.FromAddress,
		Side:        "swap",
		Amount:      amountIn,
		Price:       price,
		Value:       value,
		Time:        uint64(tx.Timestamp.Unix()),
		EventIndex:  0,
		TxIndex:     int64(tx.TxIndex),
		SwapIndex:   0,
		BlockNumber: blockNumber,
		Extra: &model.TransactionExtra{
			QuoteAddr:     tokenOut,
			QuotePrice:    fmt.Sprintf("%.6f", price),
			Type:          "swap",
			TokenSymbol:   p.getTokenSymbol(tokenIn),
			TokenDecimals: 18, // 默认值，实际应该从合约获取
		},
	}
}

// parseSwapAmounts 从Swap事件日志中解析交易金额（简化版本）
func (p *PancakeSwapExtractor) parseSwapAmounts(log *types.Log) (amountIn, amountOut *big.Int, tokenIn, tokenOut string) {
	// 简化实现：返回默认值
	// 实际应该解析日志数据获取真实金额
	return big.NewInt(1000000), big.NewInt(2000000), "", ""
}

// calculatePrice 计算交易价格
func (p *PancakeSwapExtractor) calculatePrice(amountIn, amountOut *big.Int) float64 {
	if amountIn == nil || amountOut == nil || amountIn.Cmp(big.NewInt(0)) == 0 {
		return 0.0
	}

	// 价格 = amountOut / amountIn
	amountInFloat := new(big.Float).SetInt(amountIn)
	amountOutFloat := new(big.Float).SetInt(amountOut)

	price := new(big.Float).Quo(amountOutFloat, amountInFloat)
	priceFloat64, _ := price.Float64()

	return priceFloat64
}

// getTokenSymbol 获取代币符号（简化实现）
func (p *PancakeSwapExtractor) getTokenSymbol(tokenAddr string) string {
	// 简化实现：返回地址的前6位作为符号
	if len(tokenAddr) >= 6 {
		return strings.ToUpper(tokenAddr[2:8])
	}
	return "UNKNOWN"
}

// getFactoryAddress 获取工厂地址
func (p *PancakeSwapExtractor) getFactoryAddress(log *types.Log) string {
	// 根据日志类型返回对应的工厂地址
	if len(log.Topics) == 0 {
		return ""
	}

	topic0 := log.Topics[0].Hex()
	switch topic0 {
	case pancakeSwapV2EventSig, pancakeMintV2EventSig, pancakeBurnV2EventSig, pancakePairCreatedEventSig:
		return pancakeSwapV2FactoryAddr
	case pancakeSwapV3EventSig, pancakeMintV3EventSig, pancakeBurnV3EventSig, pancakePoolCreatedEventSig:
		return pancakeSwapV3FactoryAddr
	default:
		return ""
	}
}

// createLiquidityFromLog 从日志创建流动性记录（简化版本）
func (p *PancakeSwapExtractor) createLiquidityFromLog(log *types.Log, tx *core.UnifiedTransaction, side string) *model.Liquidity {
	poolAddr := log.Address.Hex()

	return &model.Liquidity{
		Addr:    poolAddr,
		Router:  tx.ToAddress,
		Factory: p.getFactoryAddress(log),
		Pool:    poolAddr,
		Hash:    tx.TxHash,
		From:    tx.FromAddress,
		Pos:     "",
		Side:    side,
		Amount:  big.NewInt(1000000), // 简化值
		Value:   1000000.0,           // 简化值
		Time:    uint64(tx.Timestamp.Unix()),
		Key:     tx.TxHash + "_" + side,
		Extra: &model.LiquidityExtra{
			Key:    tx.TxHash + "_" + side,
			Values: []float64{1000000.0},
			Time:   uint64(tx.Timestamp.Unix()),
		},
	}
}

// createPoolFromLog 从日志创建池子记录（简化版本）
func (p *PancakeSwapExtractor) createPoolFromLog(log *types.Log, tx *core.UnifiedTransaction) *model.Pool {
	poolAddr := log.Address.Hex()

	return &model.Pool{
		Addr:     poolAddr,
		Factory:  p.getFactoryAddress(log),
		Protocol: "pancakeswap",
		Tokens:   make(map[int]string),
		Fee:      2500, // 默认费率
		Extra: &model.PoolExtra{
			Hash: tx.TxHash,
			From: tx.FromAddress,
			Time: uint64(tx.Timestamp.Unix()),
		},
	}
}
